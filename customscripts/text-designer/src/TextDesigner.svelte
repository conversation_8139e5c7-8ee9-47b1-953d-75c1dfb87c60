<script lang="ts">
	import Icon from '@iconify/svelte';
	import Fonts from '$lib/components/Fonts.svelte';
	import Colors from '$lib/components/Colors.svelte';
	import Preview from '$lib/components/Preview.svelte';
	import { Renderer } from '$lib/render.svelte';
	import { onMount, untrack } from 'svelte';
	import Quantity from '$lib/components/Quantity.svelte';
	import LetterSpacing from '$lib/components/LetterSpacing.svelte';
	import { dict } from '$lib/plugin/i18n/dict.svelte';
	import { _ } from '$lib/plugin/i18n';
	import ReverseCutPopup from '$lib/components/popups/ReverseCutPopup.svelte';
	import LetteringSizePopup from '$lib/components/popups/LetteringSizePopup.svelte';
	import ShowGuidesPopup from '$lib/components/popups/ShowGuidesPopup.svelte';
	import { fonts } from '$lib/state/fonts.svelte';
	import { ucfirst } from '$lib/utils';
	import { ui_state } from '$lib/state/ui.svelte';

	let current_locale = window.prestashop.language.iso_code;
	let locale = $state(current_locale || 'en');
	$effect.pre(() => {
		try {
			import((`$lib/plugin/i18n/output/dict.${locale}.js`))
				.then(({ default: translations }) => {
					dict.translations = translations;
				});
		} catch (e) {
			console.error(e);
			console.error(`No translations found for ${locale}`);
			dict.translations = {};
		}
	});

	const { field_name, config } = $props();

	const script_name = config.script_name || field_name;

	const renderer = new Renderer(script_name);

	let design = $derived(renderer.design);
	let ui_width = $derived(design.width);
	let ui_height = $derived(design.height);

	$effect(() => {
		renderer.renderText({
			text: untrack(() => design.text),
			font: design.font,
			character_spacing: +design.character_spacing,
			alignment: design.alignment
		});
	});

	onMount(() => {
		document.body.classList.add('text-designer');
		return window.dp.stores.calculator.listen((state) => {
			design.price = state.formatted_prices.price_ttc;
			if (state.input_fields['changed'].value === 'custom_font') {
				const custom_font = state.input_fields['custom_font'].data[0];
				if (custom_font) {
					let value = ucfirst(custom_font.filename.replace('.ttf', ''));
					fonts.add({
						value,
						label: value,
						url: window.dp_vars.urls.data_url + 'upload/' + custom_font.file
					});
					fonts.select(value);
					design.font = value;
				} else {
					fonts.reset();
					fonts.select('');
				}
			}
		});
	});

	let text_error = $state('');

	function validateText() {
		text_error = '';

		if (design.text.trim() === '') {
			text_error = _('Please enter some text');
			return false;
		}

		return true;
	}
</script>

<section>
	<div class="tc:grid tc:lg:grid-cols-3 tc:lg:grid-rows-[auto_1fr] tc:lg:grid-flow-col tc:gap-4 tc:lg:gap-2">
		<div class="tc:lg:contents">
			<p class="tc:text-center">
				{_('1. Enter your text here')}
			</p>

			<div class="tc:flex tc:flex-col tc:gap-2 tc:bg-gray-300 tc:p-2">
				<div>
					<span class="tc:text-gray-500">{_('Input multiple lines by using ENTER key.')}</span>
					<textarea
						bind:value={design.text} class="form-control tc:w-full"
						class:tc:border-red-500={text_error}
						name="your_text"
						onfocusin={() => text_error = ''}
						onkeydown={(e) => {
							if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
								e.preventDefault();
								if(validateText()) {
									renderer.updateText(design.text)
								}
							}
						}}
						rows="5"></textarea>
					{#if text_error}
						<span class="tc:text-red-500">{text_error}</span>
					{:else}
						&nbsp;
					{/if}
				</div>

				<div class="tc:flex tc:flex-wrap tc:items-center tc:gap-4">
					<label class="tc:flex tc:items-center tc:gap-1">
						<input bind:checked={design.reverse_cut} name="reverse_cut" type="checkbox" /> {_('Reverse cut')}
					</label>

					<ReverseCutPopup {script_name} />

					<button
						class="btn btn-sm btn-secondary tc:ml-auto" onclick={() => {
							if(validateText()) {
								renderer.updateText(design.text)
							}
						}} type="button">
						{_('Update preview')}
					</button>
				</div>

				<div class="tc:flex tc:flex-wrap tc:items-center tc:gap-4">
					<label class="tc:flex tc:flex-col tc:grow">
						{_('Text alignment')}
						<select bind:value={design.alignment} class="form-control" name="text_alignment">
							<option value="left">{_('Left')}</option>
							<option value="center">{_('Center')}</option>
							<option value="right">{_('Right')}</option>
						</select>
					</label>

					<LetterSpacing {renderer} />
				</div>
			</div>
		</div>

		<div class="tc:lg:contents">
			<p class="tc:text-center">
				{_('2. Select font & size')}
			</p>

			<div class="tc:flex tc:flex-col tc:gap-2 tc:bg-gray-300 tc:p-2">
				<Fonts bind:text_font={design.font} {renderer} {script_name} />

				<div class="tc:flex tc:flex-col tc:gap-2">
					<span class="tc:font-bold tc:flex tc:items-center tc:gap-2">
						{_('Lettering Size (mm)')}
						<LetteringSizePopup {script_name} />
					</span>

					<div
						class="tc:grid tc:items-center tc:gap-2 tc:w-48 tc:mx-auto"
						style="grid-template-columns: repeat(3, 1fr);">
						<label class="tc:flex tc:flex-col" for="text_height">
							{_('Height')}
						</label>
						<span></span>
						<label class="tc:flex tc:flex-col" for="text_width">
							{_('Width')}
						</label>
						<input
							bind:value={ui_height} class="form-control tc:text-center tc:w-12 tc:px-1" name="text_height"
							onkeyup={(e) => {
								if (e.key === 'Enter') {
									e.preventDefault();
									renderer.updateSize(ui_width, ui_height);
								}
							}}
							type="text">
						<span class="tc:text-center">
							<Icon icon="ic:baseline-close" />
						</span>
						<input
							bind:value={ui_width} class="form-control tc:text-center tc:w-12 tc:px-1" name="text_width"
							onkeyup={(e) => {
								if (e.key === 'Enter') {
									e.preventDefault();
									renderer.updateSize(ui_width, ui_height);
								}
							}}
							type="text">
					</div>

					<div class="tc:flex tc:flex-wrap tc:items-center tc:justify-center tc:gap-4 ">
						<button
							class="btn btn-sm btn-secondary"
							onclick={() => renderer.updateSize(ui_width, ui_height)} type="button">
							{_('Update size')}
						</button>
						<p class="tc:text-center tc:text-sm tc:max-w-48">
							{_('Enter height or width, the other dimension will be calculated accordingly.')}
						</p>
					</div>
					{#if ui_state.size_error}
						<div class="alert alert-danger">
							{ui_state.size_error}
						</div>
					{/if}
				</div>
			</div>
		</div>

		<div class="tc:lg:contents">
			<p class="tc:text-center">
				{_('3. Choose color & quantity')}
			</p>

			<div class="tc:flex tc:flex-col tc:justify-between tc:gap-2 tc:bg-gray-300 tc:p-2">
				<div class="tc:grid tc:grid-cols-2 tc:items-center tc:gap-2">
					<label for="text_color">
						{_('Color')}
					</label>

					<Colors bind:text_color={design.color} {renderer} />

					<label for="quantity">
						{_('Quantity')}
					</label>

					<Quantity {renderer} />
				</div>

				<div class="tc:flex tc:flex-wrap tc:gap-4 tc:items-center tc:justify-between">
					<span class="tc:font-bold tc:text-4xl tc:p-4">
						{design.price}
					</span>

					<button
						class="btn btn-large btn-primary"
						onclick={() => {
						document.querySelector<HTMLButtonElement>(`#add-to-cart-or-refresh [data-button-action="add-to-cart"]`)?.click()
					}} type="button">{_('Add to cart')}
					</button>
				</div>
			</div>
		</div>
	</div>

	<div>
		<Preview {renderer} />
	</div>

	<div class="tc:grid tc:grid-cols-[1fr_auto_1fr] tc:items-center tc:justify-between tc:gap-2 tc:bg-gray-300 tc:p-2">
		<label class="tc:flex tc:flex-wrap tc:justify-center tc:items-center tc:gap-2" for="text_background">
			<span class="tc:text-nowrap">{_('Surface color')}</span>
			<input
				bind:value={design.background_color} class="btn tc:p-0 tc:rounded" id="text_background" name="text_background"
				type="color" />
		</label>

		<div class="tc:text-xs tc:shrink">
			{_('Please note: The surface colour is for illustrative purposes only and won\'t be produced.')}<br>
			{_('Self-adhesive vinyl letters are individually cut and come pre-spaced with application tape applied.')}
		</div>

		<label class="tc:flex tc:items-center tc:gap-2 tc:justify-self-end" for="show_guides">
			<input bind:checked={design.show_guides} id="show_guides" name="show_guides" type="checkbox" />
			<span class="tc:text-nowrap">{_('Show guides')}</span>
			<ShowGuidesPopup {script_name} />
		</label>
	</div>
</section>

<style>
  :global(.product-add-to-cart) {
    display: none;
  }

  :global(.col-md-6:has(.images-container)) {
    display: none;
  }

  :global(.col-md-6:has(#dp-container)) {
    float: none;
    width: 100% !important;
  }
</style>
