import { BoundingBox, Font, load, type Path } from 'opentype.js';
import { debounce } from './utils';
import type { Design } from './types';
import { _ } from './plugin/i18n';
import { fonts } from './state/fonts.svelte';
import { ui_state } from '$lib/state/ui.svelte';

type Render = {
	text: string;
	font: string;
	character_spacing: number;
	alignment: 'left' | 'center' | 'right';
}

export class Renderer {
	script_name: string;
	paths: Path[] = $state([]);
	boxes: BoundingBox[] = $state([]);

	fonts_cache = new Map();

	debouncedUpdatePath = debounce(this.updatePaths.bind(this), 333);

	x = $state(0);
	x2 = $state(0);
	y = $state(0);
	width = $state(0);
	height = $state(0);
	ascender = $state(0);

	render = $state<Render>({
		text: '',
		font: '',
		character_spacing: 0,
		alignment: 'left'
	});

	design = $state<Design>({
		text: window.dp.methods.getField('textarea')?.settings.initial_value || _('Your text'),
		font: '',
		reverse_cut: false,
		alignment: 'left',
		character_spacing: '0',
		height: 30,
		ascender: 0,
		width: 30,
		size: 30,
		color: '#000000',
		quantity: 1,
		price: window.dp.stores.calculator.state.formatted_prices.price_ttc,
		background_color: '#ffffff',
		show_guides: true
	});

	constructor(script_name: string) {
		this.script_name = script_name;

		const edit_value = window.dp_vars.calculation.input_fields[script_name].value;
		try {
			if (typeof edit_value === 'string') {
				this.design = JSON.parse(edit_value);
			}
		} catch (e) {
			console.error(e);
		}

		$effect(() => {
			if (!this.render.font) return;

			const font_url = fonts.get(this.render.font)?.url;
			if (!font_url) {
				console.error('Font not found');
				return;
			}
			if (this.fonts_cache.has(this.render.font)) {
				this.debouncedUpdatePath(
					this.render.text,
					this.fonts_cache.get(this.render.font),
					+this.render.character_spacing
				);
				return;
			}
			load(font_url).then(font => {
				this.fonts_cache.set(this.render.font, font);
				this.debouncedUpdatePath(
					this.render.text,
					font,
					+this.render.character_spacing
				);
			});
		});
	}

	renderText(render: Render) {
		this.render = render;
	}

	updateText(text: string) {
		this.render = {
			...this.render,
			text
		};
	}

	updatePaths(text: string, font: Font, letterSpacing: number) {
		this.paths = [];
		this.boxes = [];
		const font_size_em = 160;

		const { sTypoLineGap, sTypoAscender } = font.tables.os2;
		const lines = text.split('\n');
		let text_box = {
			x: 0,
			y: 0,
			width: 0,
			height: 0
		};
		for (const line of lines) {
			const path = font.getPath(line, 0, text_box.height, font_size_em * 16, {
				kerning: true,
				letterSpacing
			});
			const sTypeLineGapEm = sTypoLineGap / font.unitsPerEm * font_size_em;
			const line_box = path.getBoundingBox();
			text_box.height += line_box.y2 - line_box.y1 + sTypeLineGapEm * 16;
			this.paths.push(path);
			this.boxes.push(line_box);
		}

		this.x = Math.min(...this.boxes.map(b => b.x1));
		this.x2 = Math.max(...this.boxes.map(b => b.x2));
		this.y = Math.min(...this.boxes.map(b => b.y1));
		this.width = Math.max(...this.boxes.map(b => b.x2)) - this.x;
		this.height = Math.max(...this.boxes.map(b => b.y2)) - this.y;
		this.ascender = lines.length > 1 ? 0 : sTypoAscender / font.unitsPerEm * font_size_em * 16;
		this.ascender = Math.min(this.ascender, this.height);

		// recalculate mm width
		this.design.width = Math.ceil(this.design.height * this.width / this.height);
		this.design.ascender = Math.ceil(this.design.height * this.ascender / this.height);

		if (this.design.ascender === this.design.height) {
			this.ascender = 0;
		}

		window.dp.methods.updateField('textarea', {
			value: this.design.text
		});
		window.dp.methods.updateField('height', {
			value: this.design.height
		});
		window.dp.methods.updateField('width', {
			value: this.design.width
		});
		window.dp.methods.updateField(this.script_name, {
			value: JSON.stringify({
				...$state.snapshot(this.design)
			})
		});
		window.dp.methods.recalculate();
	}

	updateSize(width: number, height: number) {
		if (!this.width || !this.height) return;

		ui_state.size_error = '';

		if (!+width) {
			ui_state.size_error = _('Please enter a valid width');
			return;
		}

		if (!+height) {
			ui_state.size_error = _('Please enter a valid height');
			return;
		}

		const changed = this.design.height !== height ? 'height' : 'width';
		if (changed === 'height') {
			this.design.width = Math.ceil(height * this.width / this.height);
			this.design.height = height;
		} else {
			this.design.height = Math.ceil(width * this.height / this.width);
			this.design.width = width;
		}

		this.design.ascender = Math.ceil(this.design.height * this.ascender / this.height);

		window.dp.methods.updateField('height', {
			value: this.design.height
		});
		window.dp.methods.updateField('width', {
			value: this.design.width
		});
		window.dp.methods.recalculate();
	}

	getAlignment(index: number) {
		switch (this.render.alignment) {
			case 'left':
				return this.x - this.boxes[index].x1;
			case 'center': {
				const line_width = this.boxes[index].x2 - this.boxes[index].x1;
				return (this.width - line_width) / 2;
			}
			case 'right': {
				return this.x2 - this.boxes[index].x2;
			}
		}
	}
}
