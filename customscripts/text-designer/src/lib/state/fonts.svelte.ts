import type { SelectOption } from '$lib/types';

export class Fonts {
	fonts_options = $state<SelectOption[]>([]);
	selected_item = $state<SelectOption>();

	load() {
		const field_options = window.dp.methods.getField('fonts')?.options;
		if (field_options) {
			this.fonts_options = Object.values(field_options).map(font => ({
				value: font.id.toString(),
				label: font.label,
				url: font.font_url
			}));
		}
	}

	select(font: string) {
		if (font) {
			const selected_font = this.fonts_options.find(f => f.value === font);
			if (selected_font) this.selected_item = selected_font;
		} else if (this.fonts_options.length > 0) {
			this.selected_item = this.fonts_options[0];
		}
	}

	add(option: SelectOption) {
		this.fonts_options.splice(this.fonts_options.findIndex(f => f.value === option.value), 1);
		this.fonts_options.push(option);
	}

	get(font: string) {
		return this.fonts_options.find(f => f.value === font);
	}

	reset() {
		this.load();
	}
}

export const fonts = new Fonts();
