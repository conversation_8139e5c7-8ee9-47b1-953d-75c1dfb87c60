describe('Text designer', () => {
	beforeEach(() => {
		cy.viewport(1200, 1000);
		cy.session('debug', () => {
			cy.setCookie('XDEBUG_SESSION', 'PHPSTORM');
		});
		cy.intercept('POST', /ps_shoppingcart\/ajax/).as('cart');
		cy.intercept('POST', /dynamicproduct\/calculator/).as('calculator');
		cy.visit('http://localhost/prestashop/new/en/home/<USER>');
	});

	it('renders text correctly', () => {
		cy.getByName('your_text').clear().type('your text\nnew line\nthird line');
		cy.getByName('text_alignment').select('Left').blur();
		cy.contains('Update preview').click();
		cy.getByTestId('text-path').should('have.length', 3);
	});

	it('applies text when ctrl enter is pressed', () => {
		cy.getByName('your_text').clear().type('your text\nnew line').type('{ctrl}{enter}');
		cy.getByTestId('text-path').should('have.length', 2);
	});

	it('should update size', () => {
		cy.getByName('your_text').clear().type('your text');
		cy.contains('Update preview').click();
		cy.getByName('text_height').should('have.value', '30');
		cy.getByName('text_width').should('have.value', '126');
		cy.getByName('text_height').clear().type('60{enter}');
		cy.getByName('text_width').should('have.value', '252');
	});

	it('should sync qty field', () => {
		cy.wait(1000);
		cy.getByName('quantity').clear().type('5').blur();
		cy.wait('@calculator');
		cy.get('#quantity_wanted').should('have.value', '5');
	});

	it('should add to cart', () => {
		cy.getByName('your_text').clear().type('your text\nnew line');
		cy.contains('Update preview').click();
		cy.wait(200);
		cy.getByName('text_alignment').select('Left').blur();
		cy.getByName('quantity').clear().type('5').blur();
		cy.wait('@calculator');
		cy.contains('Add to cart').click();
		cy.wait('@cart');
		cy.contains('Proceed to checkout').click();
		cy.get('.tc-summary').should('exist');
		cy.getByName('product-quantity-spin').should('have.value', '5');
	});

	it('adds spaces between lines', () => {
		cy.getByName('your_text').clear().type('LLLL\nLLLL');
		cy.getByName('text_alignment').select('Left').blur();
		cy.contains('Update preview').click();
	});

	it('renders text ascender', () => {
		cy.getByName('your_text').clear().type('Eggs');
		cy.getByName('text_alignment').select('Left').blur();
		cy.contains('Update preview').click();
		cy.getByTestId('ascender').should('be.visible');
	});

	it('validate text', () => {
		cy.getByName('your_text').clear();
		cy.contains('Update preview').click();
		cy.contains('Please enter some text').should('exist');
	});

	it.only('validate width and height', () => {
		cy.getByName('text_height').clear().type('0').type('{enter}');
		cy.contains('Update size').click();
		cy.contains('Please enter a valid height').should('exist');
	});

	it('should allow editing customization', () => {
		cy.getByName('your_text').clear().type('your text\nnew line');
		cy.contains('Update preview').click();
		cy.wait(200);
		cy.getByName('text_alignment').select('Center').blur();
		cy.getByName('chatacter_spacing').select('Smaller').blur();
		cy.getByName('text_height').clear().type('100{enter}');
		cy.getByName('quantity').clear().type('5').blur();

		cy.get('.tc_text_fonts').click();
		cy.get('.item').contains('Arruski').click();

		cy.get('.tc_text_colors').click();
		cy.get('.item').contains('Blue').click();
		cy.wait('@calculator');
		cy.contains('Add to cart').click();
		cy.wait('@cart');
		cy.contains('Proceed to checkout').click();
		cy.contains('Edit this customization').click();
		cy.getByName('your_text').should('have.value', 'your text\nnew line');
		cy.getByName('text_alignment').should('have.value', 'center');
		cy.getByName('chatacter_spacing').find('option').contains('Smaller').should('be.selected');
		cy.getByName('text_height').should('have.value', '100');
		cy.getByName('text_width').should('have.value', '250');
		cy.getByName('quantity').should('have.value', '5');
	});

	it('should flip text', () => {
		cy.wait(500);
		cy.getByName('reverse_cut').click();
		cy.getByTestId('text-path').should('have.length', 1);
		cy.get('.tc-reversed').should('exist');

		cy.wait(500);
		cy.getByName('reverse_cut').click();
		cy.getByTestId('text-path').should('have.length', 1);
		cy.get('.tc-reversed').should('not.exist');
	});

	it('should add a custom font', () => {
		cy.wait(500);
		cy.getByTestId('field-custom_font')
			.find('[type=file]')
			.selectFile(['cypress/fixtures/racing.ttf'], {
				force: true
			});
		cy.wait('@calculator');
		cy.wait(500);
		cy.get('.dp-delete-file').click();
		cy.wait('@calculator');
	});

	it('should update custom font', () => {
		cy.wait(500);
		cy.getByTestId('field-custom_font')
			.find('[type=file]')
			.selectFile(['cypress/fixtures/racing.ttf'], {
				force: true
			});
		cy.wait('@calculator');
		cy.wait(500);
		cy.getByTestId('field-custom_font')
			.find('[type=file]')
			.selectFile(['cypress/fixtures/vintage.ttf'], {
				force: true
			});
		cy.wait('@calculator');
	});
});
