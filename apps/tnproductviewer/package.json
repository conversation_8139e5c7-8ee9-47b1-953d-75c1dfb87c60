{"name": "tnproductviewer", "version": "1.5.13", "description": "Display your products in 3D in the product page", "type": "module", "private": true, "scripts": {"dev": "vite", "trans": "node node/trans.js", "build": "vite build", "bump": "commit-and-tag-version", "zip": "bun scripts/zip.mjs", "archive": "npm run bump && npm run zip", "publish": "npm run archive", "test": "cypress run", "test:ui": "cypress open --browser chrome | grep -v GET"}, "author": "Tuni-Soft", "license": "ISC", "devDependencies": {"@babel/core": "^7.27.4", "@babel/preset-env": "^7.27.2", "@sveltejs/vite-plugin-svelte": "^5.1.0", "@tsconfig/svelte": "^5.0.4", "@tunisoft/common": "workspace:*", "@types/node": "^22.15.31", "@vitejs/plugin-legacy": "^6.1.1", "archiver": "^7.0.1", "autoprefixer": "^10.4.21", "child-process-promise": "^2.2.1", "commit-and-tag-version": "^12.5.1", "cypress": "^14.4.1", "cypress-real-events": "^1.14.0", "cypress-watch-and-reload": "^1.10.19", "dotenv": "^16.5.0", "find-in-files": "^0.5.0", "postcss": "^8.5.5", "readdir-glob": "^2.0.1", "sass": "^1.89.2", "start-server-and-test": "^2.0.12", "svelte": "^5.33.19", "svelte-check": "^4.2.1", "svelte-preprocess": "^6.0.3", "svelte-select": "^5.8.3", "terser": "^5.42.0", "tsconfig": "workspace:*", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-bundle-analyzer": "^0.22.2", "vite-plugin-public-path": "^1.1.0", "vite-tsconfig-paths": "^5.1.4", "zx": "^8.5.5"}}