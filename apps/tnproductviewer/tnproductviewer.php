<?php

use Doctrine\DBAL\Query\QueryBuilder;
use Media as PsMedia;
use PrestaShop\PrestaShop\Core\Grid\Column\Type\Common\HtmlColumn;
use PrestaShop\PrestaShop\Core\Grid\Filter\Filter;
use PrestaShopBundle\Form\Admin\Type\YesAndNoChoiceType;
use TnProductViewer\classes\helpers\ModelsHelper;
use TnProductViewer\classes\models\ModelItem;
use TnProductViewer\classes\models\ProductViewerConfig;
use TnProductViewer\classes\module\Installer;
use TnProductViewer\classes\module\Loader;
use TnProductViewer\classes\module\Media;
use TnProductViewer\classes\module\Presenter;
use TnProductViewer\classes\module\Processor;
use TnProductViewer\classes\module\Provider;
use TnProductViewer\classes\module\Viewer;
use TnProductViewer\classes\presenter\MainConfigPresenter;
use TnProductViewer\classes\ViewerTools;
use TnProductViewer\lib\media\EntriesHelper;
use TnProductViewer\lib\pv_trans\TranslationHelper;
use TnProductViewer\libs\ModuleFixer\ModuleFixer;

require dirname(__FILE__) . '/vendor/autoload.php';

/**
 * @property bool bootstrap
 */
class TnProductViewer extends Module
{
    /** @var Processor */
    public $processor;
    /** @var Presenter */
    public $presenter;
    /** @var Viewer */
    public $viewer;
    /** @var Loader */
    public $loader;
    /** @var Installer */
    public $installer;
    /** @var Media */
    public $media;
    /** @var Provider */
    public $provider;
    /** @var FileLogger */
    public $logger;

    public $html_content = '';
    public $languages;
    public $pv_module_dir;
    public $currentIndex = 'index.php?controller=AdminModules';

    public function __construct()
    {
        $this->name = 'tnproductviewer';
        $this->tab = 'front_office_features';
        $this->version = '1.5.13';
        $this->author = 'Tuni-Soft';
        $this->need_instance = 0;
        $this->module_key = '17e08fa3f7746326be4730033ed674d7';
        $this->bootstrap = true;

        parent::__construct();
        $this->registerAutoload();
        $this->initDefines();
        $this->displayName = $this->l('Product 3D Viewer');
        $this->description = $this->l(
            'Display your products in 3D in the product page.'
        );
        $this->ps_versions_compliancy = ['min' => '1.7', 'max' => _PS_VERSION_];

        $this->initHelpers();

        $this->initVariables();
    }

    public function install()
    {
        if (!parent::install()) {
            return false;
        }

        if (!$this->installer->installDataDir()) {
            return false;
        }

        if (!$this->installer->installHooks()) {
            return false;
        }

        if (!$this->installer->installControllers()) {
            return false;
        }

        if (!$this->execSQL('sql/install')) {
            return false;
        }

        return true;
    }

    public function uninstall()
    {
        if (!$this->installer->execUninstallScript()) {
            return false;
        }

        $this->installer->uninstallControllers();

        return parent::uninstall();
    }

    public function execSQL($type)
    {
        $path = dirname(__FILE__) . '/' . $type . '.sql';

        return $this->installer->execSQLFile($path);
    }

    private function registerAutoload()
    {
        spl_autoload_register([__CLASS__, 'autoloadClass']);
    }

    public function autoloadClass($class_name)
    {
        $class_name = str_replace('\\', '/', $class_name);

        $class_path = dirname(__FILE__) . '/' . $class_name . '.php';
        if (is_file($class_path)) {
            require_once $class_path;

            return;
        }

        // libs folder
        $class_path = dirname(__FILE__) . '/libs/' . $class_name . '.php';
        if (is_file($class_path)) {
            require_once $class_path;

            return;
        }

        // data libs folder
        $class_path = _PS_ROOT_DIR_ . '/tnproductviewer/libs/' . $class_name . '.php';
        if (is_file($class_path)) {
            require_once $class_path;
        }
    }

    public function getUpgrades()
    {
        $this->loadUpgradeVersionList(
            $this->name,
            $this->version,
            '2.58.0'
        );

        return static::$modules_cache[$this->name]['upgrade']['upgrade_file_left'];
    }

    public function getDbVersion()
    {
        return Db::getInstance()->getValue(
            'SELECT version FROM ' . _DB_PREFIX_ . "module WHERE name='" . pSQL($this->name) . "'"
        );
    }

    public function initDefines()
    {
        if (!defined('_PV_FRONT_DEV_PORT_')) {
            define('_PV_FRONT_DEV_PORT_', 5001);
        }
        include dirname(__FILE__) . '/types.php';
    }

    private function initHelpers()
    {
        $this->processor = new Processor($this, $this->context);
        $this->presenter = new Presenter($this, $this->context);
        $this->viewer = new Viewer($this, $this->context);
        $this->loader = new Loader($this, $this->context);
        $this->provider = new Provider($this, $this->context);
        $this->installer = new Installer($this, $this->context);
        $this->media = new Media($this, $this->context);
        $this->logger = new FileLogger(PrestaShopLoggerInterface::DEBUG);
        $this->logger->setFilename(_PS_ROOT_DIR_ . '/var/logs/tnproductviewer.log');
    }

    private function initVariables()
    {
        $this->pv_module_dir = $this->getUrl();
        $this->smarty->assign('pv_module_dir', $this->pv_module_dir);
        $this->smarty->assign('ps_base_url', $this->getBaseUrl());
        $this->languages = Language::getLanguages();
    }

    public function getPath()
    {
        return $this->_path;
    }

    public function getModuleDir()
    {
        return '/modules/' . $this->name . '/';
    }

    public function getBaseUrl()
    {
        /* @noinspection PhpRedundantOptionalArgumentInspection */
        return $this->context->shop->getBaseURL(true);
    }

    public function getDir()
    {
        return dirname(__FILE__) . DIRECTORY_SEPARATOR;
    }

    public function getFolderPath($folder)
    {
        return $this->getDir() . $folder . (Tools::strlen($folder) ? '/' : '');
    }

    public function getUrl()
    {
        return $this->getBaseUrl() . 'modules/tnproductviewer/';
    }

    public function getFolderUrl($folder = '')
    {
        return $this->getUrl() . $folder;
    }

    public function getPathUri()
    {
        return __PS_BASE_URI__ . 'modules/' . $this->name . '/';
    }

    public function getContent()
    {
        $this->displayForm();

        return $this->html_content;
    }

    protected function postProcess()
    {
        $action = $this->processor->getCurrentAction();
        if ($action) {
            $method = 'process' . Tools::toCamelCase($action, true);
            if (method_exists($this->processor, $method)) {
                $this->processor->{$method}();
            }
        }

        if (Tools::getIsset('submitCancel')) {
            ViewerTools::redirect();
        }
    }

    private function displayForm()
    {
        if (!Tools::getIsset('view_upgrade_checker') && !Tools::getIsset('view_troubleshooter')) {
            $moduleFixer = new ModuleFixer($this);
            $this->html_content .= $moduleFixer->displayDiagnostics();
        }

        $admin_link = ViewerTools::getAdminLink();
        $id_lang = $this->context->language->id;

        $this->context->smarty->assign([
            'link' => $this->context->link,
            'req' => $admin_link,
            'pv_languages' => $this->languages,
            'pv_lang' => (int) $id_lang,
            'pv_module_link' => $this->context->link->getAdminLink('AdminModules') . '&configure=' . $this->name,
        ]);

        $this->postProcess();

        $action = $this->presenter->getCurrentAction();
        if ($action) {
            $method = 'display' . Tools::toCamelCase($action, true);
            if (method_exists($this, $method)) {
                $this->{$method}();

                return;
            }
        }

        $view_action = $this->viewer->getCurrentAction();
        if ($view_action) {
            $this->html_content .= $this->viewer->display($view_action);

            return;
        }

        $main_config_presenter = new MainConfigPresenter($this, $this->context);

        $this->context->smarty->assign([
            'main_config_html' => $main_config_presenter->display(),
            'token' => Tools::getAdminTokenLite('AdminModules'),
            'default' => '&configure=' . $this->name . '&module_name=' . $this->name,
            'version' => $this->version,
        ]);

        $this->html_content .= $this->display(__FILE__, 'views/templates/admin/display-admin-form.tpl');
    }

    /** @noinspection PhpUnused will be called by PrestaShop */
    public function hookDisplayAfterProductThumbs()
    {
        $id_product = (int) Tools::getValue('id_product');
        $product_config = ProductViewerConfig::getByProduct($id_product);
        if ($product_config->active) {
            $id_product_attribute = (int) Tools::getValue('id_product_attribute');
            if (!$id_product_attribute && Tools::getIsset('group')) {
                $groups = Tools::getValue('group');
                $id_product_attribute = (int) Product::getIdProductAttributeByIdAttributes(
                    $id_product,
                    $groups,
                    true
                );
            }
            $this->context->smarty->assign([
                'thumb_size' => ModelItem::$THUMB_SIZE,
                'models' => ModelsHelper::loadModels($id_product, $id_product_attribute),
            ]);

            return $this->display(__FILE__, 'views/templates/hook/after-product-thumbs.tpl');
        }

        return null;
    }

    /** @noinspection PhpUnused */
    public function hookDisplayHeader()
    {
        $scripts = [];
        $output = '';

        if ((int) Tools::getValue('ajax')) {
            return '';
        }

        $entries_helper = new EntriesHelper($this, $this->context);

        $is_hot_mode = ViewerTools::isHotMode(_PV_FRONT_DEV_PORT_);

        $controller_name = Tools::getValue('controller');

        if ($controller_name === 'product') {
            $id_product = (int) Tools::getValue('id_product');
            $id_product_attribute = (int) Tools::getValue('id_product_attribute');

            $product_config = ProductViewerConfig::getByProduct($id_product);
            if ($product_config->active) {
                $vars = $this->loader->getFrontProductVars($id_product, $id_product_attribute);
                PsMedia::addJsDef($vars);

                if (!$is_hot_mode) {
                    $scripts = array_merge($scripts, [
                        $entries_helper->getEntry('vite/legacy-polyfills-legacy', 'product'),
                        $entries_helper->getEntry('src/main-legacy.ts', 'product'),
                    ]);
                } else {
                    $this->smarty->assign('script', 'http://localhost:5001/src/main.ts');
                    $output .= $this->display(__FILE__, 'views/templates/hook/vite-script.tpl');
                }
            }
        }

        PsMedia::addJsDef([
            'pv_version' => $this->version,
        ]);

        $output .= $this->display(__FILE__, 'views/templates/hook/display-header.tpl');

        $this->media->addCSS([
            $this->media->getCSSDir() . 'tnproductviewer.css',
            $this->media->getThemeCSSDir() . 'tnproductviewer.css',
        ]);

        PsMedia::addJsDef([
            'pv_scripts' => array_map(function ($script) {
                return $this->getPathUri() . $script;
            }, array_unique($scripts)),
        ]);

        if (count($scripts)) {
            $output .= $this->display(__FILE__, 'views/templates/api/scripts.tpl');
        }

        return $output;
    }

    /** @noinspection PhpUnused */
    public function hookActionAdminControllerSetMedia()
    {
        $link = $this->context->link;
        $translation_helper = new TranslationHelper($this, $this->context);

        PsMedia::addJsDef([
            'pv_id_module' => $this->id,
            'ps_module_dev' => ViewerTools::isModuleDevMode(),
            'pv_public_path' => $this->getFolderUrl('lib/media/dist/'),
            'pv_translations' => $translation_helper->getAdminTranslations(),
            'pv_module_link' => $link->getAdminLink('AdminModules') . '&configure=' . $this->name,
        ]);

        $controller_name = $this->context->controller->controller_name;

        if ($controller_name === 'AdminProducts' && $this->provider->getCurrentProductID()) {
            PsMedia::addJsDef($this->loader->getAdminProductVars($this->provider->getCurrentProductID()));
        }

        if ($controller_name === 'AdminModules' && Tools::getValue('configure') === $this->name) {
            PsMedia::addJsDef([
                'pv_config' => $link->getAdminLink('ViewerMainConfig'),
                'pva_message' => $this->getAdminMessages(),
            ]);

            $entries_helper = new EntriesHelper($this, $this->context);
            $css = $entries_helper->getCSS('admin/module-form.ts');
            if (is_array($css)) {
                foreach ($css as $css_file) {
                    $this->context->controller->addCSS($this->getPathUri() . 'lib/media/dist/' . $css_file);
                }
            }
        }

        $this->context->controller->addCSS($this->_path . 'views/css/admin.css');
    }

    /** @noinspection PhpUnused */
    public function hookDisplayBackOfficeHeader()
    {
        $output = '';

        $controller = Tools::getValue('controller');
        $is_hot_mode = ViewerTools::isHotMode(_PV_FRONT_DEV_PORT_);

        if ($controller === 'AdminProducts') {
            $id_product = $this->provider->getCurrentProductID();

            if (!$id_product) {
                if ($is_hot_mode) {
                    $this->smarty->assign('script', ViewerTools::addScriptBase('admin/products-list.ts'));
                } else {
                    $entries_helper = new EntriesHelper($this, $this->context);
                    $this->smarty->assign('script', $this->getPathUri() . $entries_helper->getEntry('admin/products-list.ts'));
                }
                $output .= $this->display(__FILE__, 'views/templates/hook/vite-script.tpl');
            }
        }

        if ($controller === 'AdminModules' && Tools::getValue('configure') === $this->name) {
            if ($is_hot_mode) {
                $this->smarty->assign('script', ViewerTools::addScriptBase('admin/module-form.ts'));
            } else {
                $entries_helper = new EntriesHelper($this, $this->context);
                $this->smarty->assign('script', $this->getPathUri() . $entries_helper->getEntry('admin/module-form.ts'));
            }
            $output .= $this->display(__FILE__, 'views/templates/hook/vite-script.tpl');
        }

        return $output;
    }

    public function hookDisplayAdminProductsExtra($params)
    {
        $id_product = isset($params['id_product']) ? (int) $params['id_product'] : 0;
        $id_product = $id_product ?: Tools::getValue('id_product');

        if (!$id_product) {
            return $this->displayConfirmation(
                $this->l('You will be able to configure the module for this product after saving it.')
            );
        }

        $output = '';

        $moduleFixer = new ModuleFixer($this);
        $output .= $moduleFixer->displayDiagnostics();

        $iframe_uri = ViewerTools::isHotMode(5173) ?
          'http://localhost/dev/' :
          $this->getPathUri() . 'lib/apps/product-config/build/';
        $this->smarty->assign([
            'pv_uri' => $this->getPathUri(),
            'iframe_uri' => $iframe_uri,
            'dev_link' => ViewerTools::addQueryToUrl(
                $this->context->link->getAdminLink('ProductViewerDev'),
                [
                    'id_product' => $id_product,
                    'rand' => '_rand_',
                    'new_tab' => 1,
                ]
            ),
        ]);

        $output .= $this->display(__FILE__, 'views/templates/admin/hook/display-admin-products-extra.tpl');

        return $output;
    }

    public function getAdminMessages()
    {
        $source = basename(__FILE__, '.php');

        return [
            'success' => $this->l('Data saved', $source),
            'error' => $this->l('An error occurred', $source),
        ];
    }

    /** @noinspection PhpUnused */
    public function hookActionProductGridDefinitionModifier($params)
    {
        /** @var PrestaShop\PrestaShop\Core\Grid\Definition\GridDefinition $definition */
        $definition = $params['definition'];
        $columns = $definition->getColumns();
        $items = $columns->toArray();
        $has_image = false;
        foreach ($items as $item) {
            if ($item['id'] === 'image') {
                $has_image = true;
                break;
            }
        }
        if ($has_image) {
            $filters = $definition->getFilters();
            $filters->add(
                (new Filter('pv_active', YesAndNoChoiceType::class))
                  ->setAssociatedColumn('pv_active')
            );
            $definition->setFilters($filters);

            $columns->addAfter(
                'image',
                (new HtmlColumn('pv_active'))
                  ->setName($this->trans('3D', [], 'Admin.Global'))
                  ->setOptions([
                      'field' => 'pv_edit_link',
                      'clickable' => false,
                  ])
            );
        }
    }

    /** @noinspection PhpUnused */
    public function hookActionProductGridQueryBuilderModifier($params)
    {
        /** @var QueryBuilder $search_query */
        $search_query = $params['search_query_builder'];
        $search_query->addSelect('IF(JSON_CONTAINS(pv_config.`data`, "true", "$.active"), 1, 0) AS pv_active');
        $search_query->leftJoin(
            'p',
            _DB_PREFIX_ . $this->name . '_product_config',
            'pv_config',
            'pv_config.`id_product` = p.`id_product`'
        );

        /** @var PrestaShop\PrestaShop\Core\Search\Filters\ProductFilters $searchCriteria */
        $searchCriteria = $params['search_criteria'];
        $filters = $searchCriteria->getFilters();
        if (isset($filters['pv_active'])) {
            if ((int) $filters['pv_active'] === 1) {
                $search_query->andWhere('IF(JSON_CONTAINS(pv_config.`data`, "true", "$.active"), 1, 0) = 1');
            } else {
                $search_query->andWhere('IF(JSON_CONTAINS(pv_config.`data`, "true", "$.active"), 1, 0) = 0');
            }
        }
    }

    /** @noinspection PhpUnused */
    public function hookActionProductGridDataModifier($params)
    {
        /** @var PrestaShop\PrestaShop\Core\Grid\Data\GridData $data */
        $data = $params['data'];
        $items = $data->getRecords()->all();
        foreach ($items as $index => $item) {
            if ($item['pv_active']) {
                $this->context->smarty->assign([
                    'edit_link' => ViewerTools::addQueryToUrl(
                        $this->context->link->getAdminLink('ProductViewerDev'),
                        [
                            'id_product' => $item['id_product'],
                            'rand' => '_rand_',
                            'new_tab' => 1,
                        ]
                    ),
                    'icon_url' => $this->getUrl() . 'views/img/logos/active.png',
                ]);
                $item['pv_edit_link'] = $this->display(__FILE__, 'views/templates/hook/product-grid-icon.tpl');
            } else {
                $item['pv_edit_link'] = '';
            }
            $items[$index] = $item;
        }

        $params['data'] = new PrestaShop\PrestaShop\Core\Grid\Data\GridData(
            new PrestaShop\PrestaShop\Core\Grid\Record\RecordCollection($items),
            $data->getRecordsTotal(),
            $data->getQuery()
        );
    }
}
