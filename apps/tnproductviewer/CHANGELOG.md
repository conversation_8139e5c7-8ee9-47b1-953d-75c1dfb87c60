# Changelog

All notable changes to this project will be documented in this file. See [commit-and-tag-version](https://github.com/absolute-version/commit-and-tag-version) for commit guidelines.

## [1.5.13](https://github.com/unlocomqx/tunisoft/compare/v1.5.12...v1.5.13) (2025-07-31)

## [1.5.12](https://github.com/unlocomqx/tunisoft/compare/v1.5.11...v1.5.12) (2025-07-31)

## [1.5.11](https://github.com/unlocomqx/tunisoft/compare/v1.5.10...v1.5.11) (2025-07-29)

## [1.5.0](https://github.com/unlocomqx/tunisoft/compare/v4.1.1...v1.5.0) (2025-07-14)

### Features

* **tnproductviewer:** enhance model loading and update
  dependencies ([1039d8c](https://github.com/unlocomqx/tunisoft/commit/1039d8cebd025c3e91db025e7cf96ee8dbe18aeb))

## [1.4.0](https://github.com/unlocomqx/tunisoft/compare/v1.2.0...v1.4.0) (2025-07-05)

### Features

* **tnproductviewer:** add i18n support to models
  interface ([13da8e8](https://github.com/unlocomqx/tunisoft/commit/13da8e8a729189352d4fcfb25b66c7ba200e3188))
* **tnproductviewer:** add i18n support to product viewer
  components ([1788a85](https://github.com/unlocomqx/tunisoft/commit/1788a85c46b8e1935a7e5870431684d87a3d70b6))
* **tnproductviewer:** update French translations and improve viewer
  controls ([100f224](https://github.com/unlocomqx/tunisoft/commit/100f224f3224f26be4035818a64725903f8ffeba))
* **tnproductviewer:** update translation files with MD5
  hashes ([376084e](https://github.com/unlocomqx/tunisoft/commit/376084ec1c37b65479ec0d3dc509a8b5e1bd46f3))

## [1.3.0](https://github.com/unlocomqx/tunisoft/compare/v1.2.0...v1.3.0) (2025-07-05)

### Features

* **tnproductviewer:** add i18n support to models
  interface ([13da8e8](https://github.com/unlocomqx/tunisoft/commit/13da8e8a729189352d4fcfb25b66c7ba200e3188))
* **tnproductviewer:** add i18n support to product viewer
  components ([1788a85](https://github.com/unlocomqx/tunisoft/commit/1788a85c46b8e1935a7e5870431684d87a3d70b6))
* **tnproductviewer:** update French translations and improve viewer
  controls ([100f224](https://github.com/unlocomqx/tunisoft/commit/100f224f3224f26be4035818a64725903f8ffeba))
* **tnproductviewer:** update translation files with MD5
  hashes ([376084e](https://github.com/unlocomqx/tunisoft/commit/376084ec1c37b65479ec0d3dc509a8b5e1bd46f3))

## [1.2.0](https://github.com/unlocomqx/tunisoft/compare/v1.1.2...v1.2.0) (2025-07-05)

### Features

* **tnproductviewer:** add admin module config template for product
  setup ([355c934](https://github.com/unlocomqx/tunisoft/commit/355c934de336e0b5f246b60a1ca327482385c046))

## [1.1.2](https://github.com/unlocomqx/tunisoft/compare/v1.1.1...v1.1.2) (2025-07-05)

## [1.1.1](https://github.com/unlocomqx/tunisoft/compare/v1.1.0...v1.1.1) (2025-07-05)

## [1.1.0](https://github.com/unlocomqx/tunisoft/compare/v4.0.29...v1.1.0) (2025-07-05)

### Features

* add `displayAfterProductThumbs` hook and refine model
  handling ([7b8c21a](https://github.com/unlocomqx/tunisoft/commit/7b8c21ab201cdf0d99deb49226e2bdc19a9e4a7d))
* add `Modal` component and update product viewer
  logic ([0d93123](https://github.com/unlocomqx/tunisoft/commit/0d9312337d1571590592f6da2858af70d115fdb2))
* add `onsave` prop and enhance checkbox logic in
  ShareModel ([35d6f1c](https://github.com/unlocomqx/tunisoft/commit/35d6f1c952a31ef1833c56de2112843632846e0a))
* add 3D model integration into product
  images ([243b649](https://github.com/unlocomqx/tunisoft/commit/243b649d934aa4a7668153099823bcc9dc915081))
* add 3D model preview with GLB format
  support ([e7b50ed](https://github.com/unlocomqx/tunisoft/commit/e7b50ed3e3eaa5e3e82d19323232b7097fd93ad9))
* add automatic camera positioning based on scene
  bounds ([9bc23fa](https://github.com/unlocomqx/tunisoft/commit/9bc23fa321f50e13a565db5b8e821ead06b17465))
* add combination model table and update product model
  logic ([d602447](https://github.com/unlocomqx/tunisoft/commit/d602447f7e4c5b90a15eb099ed0fc67c9603dc24))
* add Cypress test script and integrate into publish
  workflow ([3713c26](https://github.com/unlocomqx/tunisoft/commit/3713c26d235565f096cc8f486965a8a716ae601f))
* add dynamic `label` prop to Viewer and update model rendering
  logic ([c9d8854](https://github.com/unlocomqx/tunisoft/commit/c9d88544e09b2fc5213f6e68b0d395127f5fe859))
* add file upload functionality for model
  management ([3c88fe4](https://github.com/unlocomqx/tunisoft/commit/3c88fe4dbd942784dc8835774d8645f4a7394b5a))
* add Gizmo support and enhance Viewer
  initialization ([c2f8934](https://github.com/unlocomqx/tunisoft/commit/c2f89348406ba45e3f523c1f8a4d61f4bb1e26c7))
* add interactive 3D model preview
  modal ([3648862](https://github.com/unlocomqx/tunisoft/commit/364886257166da001484cd71660e782bb6a1bef2))
* add loading spinner to Model component and onload callback in
  ModelScene ([b3b7ae0](https://github.com/unlocomqx/tunisoft/commit/b3b7ae057825bbe3a313edd7b11eb0a5f9d53bdb))
* add localized "Thumbnail" label with updated hash in
  ProductTranslations ([c02af37](https://github.com/unlocomqx/tunisoft/commit/c02af373c60109cdbd7800b9d8b85e0b29a289cd))
* add model helpers and clean up configuration
  schemas ([d832a33](https://github.com/unlocomqx/tunisoft/commit/d832a3314fe8df2d8b52f35d44665b4a9b0670e1))
* add model upload functionality with file
  validation ([8f7af82](https://github.com/unlocomqx/tunisoft/commit/8f7af827452ebdb6694b5fafa93f19c38f3b0dc2))
* add ModelItem class and database
  table ([5167895](https://github.com/unlocomqx/tunisoft/commit/51678951672882273dbdd1575972d2be01bef8e0))
* add Models configuration section to
  ProductViewer ([7c0fb7b](https://github.com/unlocomqx/tunisoft/commit/7c0fb7b60693a8cf0a63a97307c591b353884bd9))
* add near and far clipping plane adjustments for improved camera
  setup ([9f0cca5](https://github.com/unlocomqx/tunisoft/commit/9f0cca5bbc2e5e151f9184939ab95841653e2b6d))
* add persistent auto-rotation state and improve viewer
  UX ([ae0ac7e](https://github.com/unlocomqx/tunisoft/commit/ae0ac7e61c9dbb258f6b14244a22b940b70167c2))
* add preview update functionality to Viewer
  component ([d5aaa7e](https://github.com/unlocomqx/tunisoft/commit/d5aaa7e86fa4d103b99a3dc986107888ff37df8b))
* add ShareModel component for assigning models to product
  attributes ([0a4bc55](https://github.com/unlocomqx/tunisoft/commit/0a4bc55dcd4afd8cd05149da1e44f3b40566dfaf))
* add shift-click functionality for bulk checkbox selection in
  ShareModel ([6b50458](https://github.com/unlocomqx/tunisoft/commit/6b504586d1dde2f7305f423acfd29b700d0a992f))
* add Threlte and Three.js dependencies for 3D model
  support ([dbf190c](https://github.com/unlocomqx/tunisoft/commit/dbf190c8bb6dd52bf7191991434069c80becf0d8))
* add Viewer open state binding and button for model
  preview ([4c45299](https://github.com/unlocomqx/tunisoft/commit/4c45299978798270efd3f68fcd138c49df6d9411))
* add watermark overlay to model
  previews ([821d5f3](https://github.com/unlocomqx/tunisoft/commit/821d5f350545c6cc97df036c4be04ad00a6ad1fe))
* add watermark to thumbnail previews and improve image
  processing ([2795380](https://github.com/unlocomqx/tunisoft/commit/27953803e24862f591fc0fe9de31ad336d7249bd))
* adjust camera FOV and position for improved model
  framing ([b748a79](https://github.com/unlocomqx/tunisoft/commit/b748a79421ac6f83bd9924cb974a735b803a0e8e))
* enable shadow casting for directional lights and set Viewer open state to
  true ([389cacb](https://github.com/unlocomqx/tunisoft/commit/389cacbe6edce9a5bd05a040cf855d4f2e281612))
* enhance 3D model preview system with automatic PNG
  generation ([15eee4c](https://github.com/unlocomqx/tunisoft/commit/15eee4c4825a67ba17a85e3f17265a8ec12c3fdc))
* enhance 3D model viewer with auto-rotation and improved camera
  positioning ([846a0c8](https://github.com/unlocomqx/tunisoft/commit/846a0c885bdfde8bc3bacfef637579501a71b3d3))
* enhance 3D model viewer with configurable camera
  position ([6324b4c](https://github.com/unlocomqx/tunisoft/commit/6324b4c25c00313fe2817400b359c32897ed4535))
* enhance 3D viewer responsiveness and camera
  controls ([5d190b7](https://github.com/unlocomqx/tunisoft/commit/5d190b74b55153ff751ee098dcdba06fb95be4e7))
* enhance modal and model rendering logic with improved UI and
  interactivity ([f1b6408](https://github.com/unlocomqx/tunisoft/commit/f1b64082897bb8e7bafde3ccc5ead1633a95e765))
* enhance model deletion confirmation and update column name in
  Viewer ([695e5ef](https://github.com/unlocomqx/tunisoft/commit/695e5ef8f50b9f3aeaa930ca18d60abef14bf85a))
* enhance model management UI with loading states and
  actions ([ce15135](https://github.com/unlocomqx/tunisoft/commit/ce15135302ec2440f5109a557ecf2b2a750a60b8))
* enhance model management with combination support and file
  deletion ([50ac20a](https://github.com/unlocomqx/tunisoft/commit/50ac20a0903ca4faffde618aa272137c0fd1cad7))
* enhance model UI with hover controls and improved
  layout ([746dcd9](https://github.com/unlocomqx/tunisoft/commit/746dcd9d1c8f256d242d361df17aaa948aacdf1c))
* enhance product update handling and extend Cypress
  tests ([203184d](https://github.com/unlocomqx/tunisoft/commit/203184d468eb4c4e9485d9f8ef025c27a7d108d5))
* enhance product viewer setup and cleanup
  logic ([ca7db46](https://github.com/unlocomqx/tunisoft/commit/ca7db4661b2dd71bd31fc51b1d633cf8014a7366))
* enhance thumbnail handling and improve model interaction
  logic ([d362736](https://github.com/unlocomqx/tunisoft/commit/d3627366c386142f99bd32f6a7457048a1726289))
* enhance Viewer state logic and texture handling in
  ModelScene ([b27bac4](https://github.com/unlocomqx/tunisoft/commit/b27bac45a58c1ef9dd4877506c96326cbcf753c0))
* highlight active combination in ShareModel
  table ([ed35e27](https://github.com/unlocomqx/tunisoft/commit/ed35e27c663028c04333ccce54c27159ce516fb6))
* implement Scene component with orbit controls and model
  loading ([15a65cd](https://github.com/unlocomqx/tunisoft/commit/15a65cd4ffc143211393ce504cdc709a1b416482))
* implement share functionality for models with
  combinations ([d6717c8](https://github.com/unlocomqx/tunisoft/commit/d6717c82a01e007758729f7c38066fac685c8190))
* improve lighting setup and format camera
  configuration ([f43a14a](https://github.com/unlocomqx/tunisoft/commit/f43a14ae2a87f43128394b6bff46f18ac827da50))
* improve lighting, camera aspect ratio, and model rendering
  flow ([1b21b06](https://github.com/unlocomqx/tunisoft/commit/1b21b0631d1654f2054de0060bae837fc2801186))
* improve model management UX and
  validation ([0fe0eb7](https://github.com/unlocomqx/tunisoft/commit/0fe0eb71297b0b6d7d1a527ef205edc10a406de1))
* improve model preview handling and UI
  interaction ([4cb74a8](https://github.com/unlocomqx/tunisoft/commit/4cb74a8ce9da893bc0ed710e58cb5b39a081e6c4))
* improve model section layout with larger preview
  images ([2a5c5ff](https://github.com/unlocomqx/tunisoft/commit/2a5c5ff00ec91c6fac9ef72bd93a58876f19276c))
* improve product modal interactions and update
  styles ([531474a](https://github.com/unlocomqx/tunisoft/commit/531474afff3394789ce286f0b7be4406975ed77b))
* improve ShareModel dialog and refine translation
  function ([4b741b0](https://github.com/unlocomqx/tunisoft/commit/4b741b073938caa849de6a9f91d701e0fcc37e43))
* include `id_product_attribute` in model values and update SQL
  query ([2770e72](https://github.com/unlocomqx/tunisoft/commit/2770e72b7ed8c6120b6caa649bd622fae282f1ff))
* refine camera settings and enhance lighting
  configuration ([87fca65](https://github.com/unlocomqx/tunisoft/commit/87fca65e3978745fcdd8891bc42e67b191e02239))
* simplify model handling and enhance thumbnail
  rendering ([d428011](https://github.com/unlocomqx/tunisoft/commit/d42801123d87baf94bbd197a4c9c926e2a75c338))
* switch from PerspectiveCamera to OrthographicCamera for model
  viewing ([bc29bfb](https://github.com/unlocomqx/tunisoft/commit/bc29bfb8749be5714a0547d7f4e2d760f39d61fe))
* update combination model logic and enhance database
  handling ([04399b6](https://github.com/unlocomqx/tunisoft/commit/04399b66fa61794efb334fd5bca422d592c8d93a))
* update environment texture handling and improve model preview
  logic ([271cb56](https://github.com/unlocomqx/tunisoft/commit/271cb56d3d4c6c054689d1f7f50a26ccc176a5ac))
* update model rendering and modal logic with UI
  improvements ([3fb8fd9](https://github.com/unlocomqx/tunisoft/commit/3fb8fd9acb131564cda7dfcaff07d5a5c32ecf5d))
* update Viewer initialization and add sunrise HDR
  texture ([7bb1b6e](https://github.com/unlocomqx/tunisoft/commit/7bb1b6e4de303ecfc6afc38ec2d322cf5000c7e2))
* update viewport configuration in Cypress test and add responsive styling to Model
  component ([9641b24](https://github.com/unlocomqx/tunisoft/commit/9641b240d1e9469467605c37e4eff7fbbc6dfb63))

### Bug Fixes

* adjust button dimensions in model upload
  route ([f1d6af8](https://github.com/unlocomqx/tunisoft/commit/f1d6af8a79ed9b3d7b079f63311ef880bbd0d17a))
* close Dialog on preview update in Viewer
  component ([5cc54af](https://github.com/unlocomqx/tunisoft/commit/5cc54afe6eea93387a8d5fcdd3c8259387c4c900))
* improve model file upload handling and
  naming ([9470f8f](https://github.com/unlocomqx/tunisoft/commit/9470f8f75e9fb4c7020120735116d4146bb99acd))
* improve model section UI layout with
  flexbox ([cc5cb1a](https://github.com/unlocomqx/tunisoft/commit/cc5cb1ac170a38b3a09ef9d9c4a2752ed076b890))
* improve PNG export timing and preview
  display ([ac97965](https://github.com/unlocomqx/tunisoft/commit/ac979656174c1b68704aa31507977b6590362f56))
* remove deleted column from model table
  schema ([66fa059](https://github.com/unlocomqx/tunisoft/commit/66fa059085b913b8bed3ad8d784a32a135b3894b))
* remove invalid table alias in ModelItem SQL
  query ([4f6b7c3](https://github.com/unlocomqx/tunisoft/commit/4f6b7c384b7e373b3b8be68968b29c5bf2602495))
* remove unnecessary camera check in PNG
  export ([aca030b](https://github.com/unlocomqx/tunisoft/commit/aca030b8ba991516bd741fbf0d2fdfd3866066a5))
* set ShareModel dialog to closed by
  default ([fc35768](https://github.com/unlocomqx/tunisoft/commit/fc35768d99715b19d2e2580b884f3e4b10812fc3))
* update i18n lookup function to use FNV-1a hash
  keys ([05d86fd](https://github.com/unlocomqx/tunisoft/commit/05d86fd0c51cdbede4172c9d2132ef2ddf7be8f2))
* update namespace and code formatting in ModelHelper and
  ModelItem ([bd4fc37](https://github.com/unlocomqx/tunisoft/commit/bd4fc370b09e9cbf34671a7dba968a84866d9f40))
