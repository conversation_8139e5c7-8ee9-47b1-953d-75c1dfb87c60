<?php

namespace TnProductViewer\lib\pv_trans;

class TranslationHelper
{
    /** @var \TnProductViewer */
    public $module;
    /** @var \Context */
    public $context;

    public function __construct($module, $context)
    {
        $this->module = $module;
        $this->context = $context;
    }

    public function getAdminTranslations()
    {
        $source = 'TranslationHelper';

        return [
            // start admin 3938
            // end admin
        ];
    }
}
