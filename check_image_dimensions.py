from PIL import Image
import os

# List of images to check
images = [
    '/Users/<USER>/code/prestashop/new/dynamicproduct/scripts/text_designer/lettering-size.png',
    '/Users/<USER>/code/prestashop/new/dynamicproduct/scripts/text_designer/custom-lettering.png',
    '/Users/<USER>/code/prestashop/new/dynamicproduct/scripts/text_designer/lettering-fields.png',
    '/Users/<USER>/code/prestashop/new/dynamicproduct/scripts/text_designer/normal-cut.png',
    '/Users/<USER>/code/prestashop/new/dynamicproduct/scripts/text_designer/reverse-cut.png'
]

# Expected dimensions from HTML
expected_dimensions = {
    'lettering-size.png': (1344, 384),
    'custom-lettering.png': (1344, 654),
    'lettering-fields.png': (730, 290),
    'normal-cut.png': (1096, 204),
    'reverse-cut.png': (1096, 204)
}

# Check dimensions
for image_path in images:
    if os.path.exists(image_path):
        try:
            with Image.open(image_path) as img:
                actual_width, actual_height = img.size
                filename = os.path.basename(image_path)
                expected_width, expected_height = expected_dimensions.get(filename, (0, 0))

                print(f"Image: {filename}")
                print(f"  Actual dimensions: {actual_width}x{actual_height}")
                print(f"  Expected dimensions: {expected_width}x{expected_height}")

                if (actual_width, actual_height) != (expected_width, expected_height):
                    print(f"  MISMATCH: Dimensions don't match!")
                else:
                    print(f"  OK: Dimensions match.")
                print()
        except Exception as e:
            print(f"Error processing {image_path}: {e}")
    else:
        print(f"Image not found: {image_path}")
